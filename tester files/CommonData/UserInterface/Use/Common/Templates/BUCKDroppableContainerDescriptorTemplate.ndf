template BUCKDroppableContainerDescriptor
[
    // ++ BUCKContainerDescriptor
    ElementName : string = "",
    UniqueName : string = "",
    ComponentFrame : TUIFramePropertyRTTI = TUIFramePropertyRTTI(),

    PointerEventsToAllow : int = ~/EAllowablePointerEventType/None,
    HidePointerEvents : bool = false,

    GridAlign : bool = false,

    FitStyle : int = ~/ContainerFitStyle/None,
    ChildFitToContent : bool = false,

    ClipContent : bool = false,
    IsClippable : bool = true,

    ComponentStateLocked : bool = false,
    // -- BUCKContainerDescriptor
]
is TBUCKDroppableContainerDescriptor
(
    // ++ BUCKContainerDescriptor
    ElementName = <ElementName>
    UniqueName = <UniqueName>
    ComponentFrame = <ComponentFrame>

    PointerEventsToAllow = <PointerEventsToAllow>
    HidePointerEvents = <HidePointerEvents>

    GridAlign = <GridAlign>

    FitStyle = <FitStyle>
    ChildFitToContent = <ChildFitToContent>

    ClipContent = <ClipContent>
    IsClippable = <IsClippable>

    ComponentStateLocked = <ComponentStateLocked>
    // -- BUCKContainerDescriptor
)
