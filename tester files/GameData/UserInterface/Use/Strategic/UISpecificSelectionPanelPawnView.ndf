
SelectionPawnPanelWidth is 345.0

template BUCKSpecificSelectionPanelPawnMainComponent
[
    ContainsCubeAction : bool,
    HasSpotlight : bool,
] is BUCKListDescriptor
(
    ElementName = "SmartGroupsConfigurationMainPanel"

    ComponentFrame = TUIFramePropertyRTTI
    (
        AlignementToAnchor = [0.0, 1.0]
        AlignementToFather = [0.0, 1.0]
    )

    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    Axis  = ~/ListAxis/Horizontal

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKListDescriptor
            (
                ElementName = "SmartGroupsConfigurationLeftList"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [~/SelectionPawnPanelWidth, 0.0]
                    AlignementToAnchor = [0.0, 1.0]
                    AlignementToFather = [0.0, 1.0]
                )

                Axis = ~/ListAxis/Vertical

                Elements =
                (
                    <ContainsCubeAction> ?
                    [
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = BUCKListDescriptor
                            (
                                Axis = ~/ListAxis/Horizontal
                                FirstMargin = TRTTILength(Magnifiable = 5)

                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    MagnifiableWidthHeight = [0.0, 36.0]
                                )

                                Elements =
                                [
                                    BUCKListElementDescriptor
                                    (
                                        ComponentDescriptor = BUCKContainerDescriptor
                                        (
                                            UniqueName = "WaitUntilNextTurnContainer"

                                            ComponentFrame = TUIFramePropertyRTTI
                                            (
                                                MagnifiableWidthHeight = [50.0, 38.0]
                                            )

                                            Components =
                                            [
                                                PanelRoundedCorner
                                                (
                                                    ComponentFrame = TUIFramePropertyRTTI
                                                    (
                                                        RelativeWidthHeight = [1.0, 1.0]
                                                        MagnifiableWidthHeight = [0, 0]
                                                        AlignementToFather = [0.5, 0.5]
                                                        AlignementToAnchor = [0.5, 0.5]
                                                    )

                                                    Radius = 3
                                                    BackgroundBlockColorToken = 'SM_Noir'
                                                    RoundedVertexes = [false, true, true, false]
                                                    HasBorder = false
                                                ),
                                            ]
                                        )
                                    ),
                                    BUCKListElementDescriptor
                                    (
                                        ComponentDescriptor = BUCKContainerDescriptor
                                        (
                                            UniqueName = "SpecificStrategicCubeActionViewMainContainer"
                                            ComponentFrame = TUIFramePropertyRTTI()
                                            FitStyle = ~/ContainerFitStyle/FitToContent
                                        )
                                    ),
                                ]
                            )
                        )
                    ] : []
                ) +
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKListDescriptor
                        (
                            ElementName = "SmartGroupsConfigurationSubList"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                MagnifiableWidthHeight = [~/SelectionPawnPanelWidth, 0.0]
                            )

                            Axis = ~/ListAxis/Vertical

                            HasBorder  = true
                            BorderLineColorToken = 'SM_Noir'
                            BorderThicknessToken = "1"

                            HasBackground = true
                            BackgroundBlockColorToken = 'SM_Feldgrau'

                            HidePointerEvents = true

                            Elements =
                            [
                                BUCKListElementDescriptor
                                (
                                    ComponentDescriptor = ~/PawnDivisionInfosBanner
                                ),
                                BUCKListElementDescriptor
                                (
                                    ComponentDescriptor = ~/PawnCharacteristicList
                                ),
                                BUCKListElementDescriptor
                                (
                                    ComponentDescriptor = ~/PawnUnitTypesList
                                ),
                                BUCKListElementDescriptor
                                (
                                    ComponentDescriptor = BUCKContainerDescriptor
                                    (
                                        ComponentFrame = TUIFramePropertyRTTI
                                        (
                                            MagnifiableWidthHeight = [0,3]
                                            RelativeWidthHeight = [1,0]
                                            ))
                                        BackgroundBlockColorToken = 'SM_Feldgrau'

                                    ),
                                BUCKListElementDescriptor
                                (
                                    ComponentDescriptor = CombatGroupsTabsMainContainer(HasSpotlight = <HasSpotlight>)
                                ),
                            ]
                        )
                    ),
                ]
            )
        ),

        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ElementName = "SmartGroupDisplayerContainer"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToAnchor = [0.0, 1.0]
                    AlignementToFather = [0.0, 1.0]
                )

                FitStyle = ~/ContainerFitStyle/FitToContent

                Components =
                [
                    // On envoit UISpecificStrategicSmartGroupsDisplayerViewDescriptor via cpp
                    // et donc cet element : SmartGroupRackContainerStrategic
                ]
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

template CombatGroupsTabsMainContainer
[
    HasSpotlight : bool,
] is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [~/SelectionPawnPanelWidth, 0.0]
    )

    Axis = ~/ListAxis/Vertical

    HasBackground = true
    BackgroundBlockColorToken =  "SM_Grullo"

    FirstMargin = TRTTILength(Magnifiable = 10.0)
    InterItemMargin = TRTTILength (Magnifiable = 8.0)
    LastMargin = TRTTILength(Magnifiable = 10.0)

    ElementName = "CombatGroupsTabsMainContainer"

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "PawnRoleAndUnitsLeft"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                    MagnifiableWidthHeight = [0.0, 16.0]
                    AlignementToFather  = [0.5, 0.0]
                    AlignementToAnchor  = [0.5, 0.0]
                )

                ParagraphStyle = paragraphStyleTextCenter
                TextStyle = "Default"
                BigLineAction = ~/BigLineAction/ResizeFont

                TextDico = ~/LocalisationConstantes/dico_interface_ingame

                TypefaceToken = "Liberator"
                TextColor = "SM_Ebony"
                TextSize = "16"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKRackDescriptor
            (
                ElementName = "CombatGroupsTabRack"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                )

                Axis = ~/ListAxis/Vertical
                ChildFitToContent = true
                InterItemMargin = TRTTILength (Magnifiable = 8.0)
                BladeDescriptor = ~/CombatGroupsTabInStrategic
            )
        ),
    ]

    ForegroundComponents = (<HasSpotlight> ? [~/SpotLight_PawnInfoPanel] : []) +
    [
        BUCKDroppableContainerDescriptor
        (
            ElementName = "CombatGroupsTabsMainContainerDroppable"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )
        )
    ]
)

//-------------------------------------------------------------------------------------

SpotLight_PawnInfoPanel is BUCKSpotlightDescriptor
(
    UniqueName = "SpotLight_PawnInfoPanel"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )
)

//-------------------------------------------------------------------------------------

CombatGroupsTabInStrategic is BUCKDraggableContainerDescriptor
(
    ElementName = "CombatGroupsTabDraggable"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [320.0, 22.0]
        AlignementToAnchor = [0.5, 0.0]
        AlignementToFather = [0.5, 0.0]
    )

    PointerDistanceBeforeDrag = 5.0

    Components =
    [
        BUCKButtonDescriptor
        (
            ElementName = "CombatGroupsTab"

            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            RadioButtonManager = ~/GroupsTabManager
            IsTogglable = true
            LeftClickSound = SoundEvent_SteelmanPawnCombatGroup
            HidePointerEvents = false
            MaskEvents = false

            Components =
            [
                PanelRoundedCorner
                (
                    ElementName = "CombatGroupRoundedContainer"
                    BackgroundBlockColorToken = "SM_Feldgrau"
                    BorderLineColorToken = "SM_RifleGreen"
                    Radius = 5
                ),

                BUCKTextDescriptor
                (
                    ElementName = "CombatGroupsTabText"
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 1.0]
                        AlignementToFather  = [0.5, 0.5]
                        AlignementToAnchor  = [0.5, 0.5]
                    )

                    ParagraphStyle = ~/paragraphStyleTextCenter
                    TextStyle = "Default"
                    BigLineAction = ~/BigLineAction/ResizeFont

                    TextDico = ~/LocalisationConstantes/dico_interface_ingame

                    TypefaceToken = "Liberator"
                    TextColor = "SM_paleSilver"
                    TextSize = "16"

                    Components =
                    [
                        BUCKTextDescriptor
                        (
                            ElementName = "CombatGroupsUnitsLeft"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 0.0]
                                MagnifiableWidthHeight = [0.0, 14.0]
                                AlignementToFather  = [1.0, 0.5]
                                AlignementToAnchor  = [1.0, 0.5]
                            )

                            ParagraphStyle = ~/paragraphStyleTextRightAlign
                            TextStyle = "Default"
                            BigLineAction = ~/BigLineAction/ResizeFont

                            TextPadding = TRTTILength4(Magnifiable = [0.0, 0.0, 5.0, 0.0])

                            TypefaceToken = "Liberator"
                            TextColor = "SM_paleSilver"
                            TextSize = "14"
                        )
                    ]
                ),
            ]
        )
    ]
)

//-------------------------------------------------------------------------------------

PawnDivisionInfosBanner is BUCKListDescriptor
(
    ElementName = "PawnDivisionInfosBanner"

    ComponentFrame = TUIFramePropertyRTTI()

    Axis = ~/ListAxis/Horizontal
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    HasBackground = true
    BackgroundBlockColorToken = 'SM_Ebony'

    Elements =
    [
        // icone du bataillon
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextureDescriptor
            (
                ElementName = 'NATOIcon'
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [45.0, 40.0]
                    AlignementToFather = [0.0, 0.5]
                    AlignementToAnchor = [0.0, 0.5]
                )

                TextureColorToken = 'SM_paleSilver'
                TextureFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [40.0, 40.0]
                    AlignementToAnchor = [0.5, 0.5]
                    AlignementToFather = [0.5, 0.5]
                )

                Components =
                [
                    BUCKSpecificStrategicHintableArea
                    (
                        HintBodyToken = "sm_icoba"
                        DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                    )
                ]
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/PawnName
        ),
        BUCKListElementDescriptor
        (
            ExtendWeight = 1.0
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI()
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextureDescriptor
            (
                ElementName = "DivisionIcon"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [40.0, 40.0]
                    AlignementToFather = [0.0, 0.5]
                    AlignementToAnchor = [0.0, 0.5]
                )

                ResizeMode = ~/TextureResizeMode/UserDefined

                TextureDrawer = 'ColorMultiply_NoGrayscale'

                Components =
                [
                    BUCKSpecificStrategicHintableArea
                    (
                        ElementName = "DivisionIconHint"
                        DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                    )
                ]
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

PawnName is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        AlignementToAnchor = [0.0, 1.0]
        AlignementToFather = [0.0, 1.0]
    )

    Axis = ~/ListAxis/Vertical
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    FirstMargin = TRTTILength(Magnifiable = 4.0)
    LastMargin = TRTTILength(Magnifiable = 4.0)
    InterItemMargin = TRTTILength(Magnifiable = 2.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "PawnName"

                ComponentFrame = TUIFramePropertyRTTI()

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Left
                    VerticalAlignment = UIText_VerticalCenter
                )

                TextStyle = "Default"

                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/FitToContent

                TypefaceToken = "Liberator"
                BigLineAction = ~/BigLineAction/MultiLine

                TextDico = ~/LocalisationConstantes/dico_units

                TextColor = "SM_paleSilver"
                TextSize = "20"

                Hint = BUCKSpecificStrategicHintableArea
                (
                    DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                    HintTitleToken = ''
                    HintBodyToken = 'sm_nomba'
                    HintExtendedToken = ''
                )
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "DivisionName"

                ComponentFrame = TUIFramePropertyRTTI()

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                )

                TextStyle = "Default"

                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/FitToContent

                TypefaceToken = "Eurostyle_Italic"
                BigLineAction = ~/BigLineAction/ResizeFont

                TextDico = ~/LocalisationConstantes/dico_units

                TextColor = "SM_paleSilver"
                TextSize = "12"

                Hint = BUCKSpecificStrategicHintableArea
                (
                    DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                    HintTitleToken = ''
                    HintBodyToken = 'sm_divna'
                    HintExtendedToken = ''
                )
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

PawnCharacteristicList is BUCKListDescriptor
(
    ElementName = "PawnCharacteristicList"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 36.0]
        AlignementToAnchor = [0.5, 0.0]
        AlignementToFather = [0.5, 0.0]
    )

    Axis = ~/ListAxis/Horizontal

    InterItemMargin = TRTTILength (Magnifiable = 2.0)

    HasBackground = true
    BackgroundBlockColorToken = 'SM_Feldgrau'

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DisplayAttackAndDefenseValueForPawn
            (
                ElementName = "PowerValue"
                TextColor = "BlancEquipe"
                TextToken = "AttStre"
                TextToken_title = "sm_piatt"
                HintTitleToken = "sm_battt"
                HintBodyToken =  "sm_battc"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DisplayAttackAndDefenseValueForPawn
            (
                ElementName = "ShieldValue"
                TextColor = "BlancEquipe"
                TextToken = "DefStre"
                TextToken_title = "sm_pidef"

                HintTitleToken = "sm_bdeft"
                HintBodyToken =  "sm_bdefc"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DisplayAttackAndDefenseValueForPawn
            (
                ElementName = "FatigueValue"
                TextColor = "BlancEquipe"
                TextToken = "T_SLASH2"
                TextToken_title = "sm_pifat"

                HintTitleToken = "sm_fatt"
                HintBodyToken =  "sm_fatc"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DisplayAttackAndDefenseValueForPawn
            (
                ElementName = "ActionPointsValue"
                TextColor = "BlancEquipe"
                TextToken = "T_SLASH2"
                TextToken_title = "sm_piapt"

                HintTitleToken = "sm_pat"
                HintBodyToken =  "sm_pac"
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

template UnitTypeIconAndAmountList
[
    UnitTypeName : string,
    TextureToken : string,
] is BUCKListDescriptor
(
    ElementName = "UnitTypeIconAndAmountList_" + <UnitTypeName>

    ComponentFrame = TUIFramePropertyRTTI()
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    Axis = ~/ListAxis/Vertical
    LastMargin = TRTTILength(Magnifiable = 0.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextureDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [26.0, 26.0]
                    AlignementToAnchor = [0.5, 0.0]
                    AlignementToFather = [0.5, 0.0]
                )

                TextureFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                    // On fait dépasser le bord bas vide des images pour ne pas trop décaler le texte dessous
                    // MagnifiableWidthHeight = [0.0, 5.0]
                )

                TextureToken = <TextureToken>
                TextureColorToken = "SM_paleSilver"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "UnitTypeAmount_" + <UnitTypeName>
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [37.0, 0.0]
                    AlignementToAnchor = [0.5, 0.0]
                    AlignementToFather = [0.5, 0.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_Up
                    InterLine = 0
                )

                TextStyle = "Default"

                VerticalFitStyle = ~/FitStyle/FitToContent

                TypefaceToken = "Eurostyle"
                BigLineAction = ~/BigLineAction/CutByDots

                TextDico = ~/LocalisationConstantes/dico_interface_ingame

                TextColor = "BlancEquipe"
                TextSize = "11"
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

PawnUnitTypesList is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 0.0]
        AlignementToAnchor = [0.5, 0.0]
        AlignementToFather = [0.5, 0.0]
    )

    FitStyle = ~/ContainerFitStyle/FitToContentVertically

    HasBackground = true
    BackgroundBlockColorToken = "SM_RifleGreen"

    Components =
    [
        BUCKGridDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                AlignementToAnchor = [0.5, 0.0]
                AlignementToFather = [0.5, 0.0]
            )

            InterElementMargin = TRTTILength2(Magnifiable = [2.0, 2.0])

            GridElements = MAP
            [
                ([0, 0], UnitTypeIconAndAmountList
                    (
                        UnitTypeName = "Command"
                        TextureToken = "UseInGame_Transport_COMMAND"
                    )
                ),
                ([0, 1], UnitTypeIconAndAmountList
                    (
                        UnitTypeName = "Reco"
                        TextureToken = "UseInGame_Transport_RECO"
                    )
                ),
                ([0, 2], UnitTypeIconAndAmountList
                    (
                        UnitTypeName = "Supply"
                        TextureToken = "Texture_RTS_H_supply"
                    )
                ),
                ([0, 3], UnitTypeIconAndAmountList
                    (
                        UnitTypeName = "Infantry"
                        TextureToken = "UseInGame_Transport_REGINF"
                    )
                ),
                ([0, 4], UnitTypeIconAndAmountList
                    (
                        UnitTypeName = "Tank"
                        TextureToken = "UseInGame_Transport_Tank"
                    )
                ),
                ([0, 5], UnitTypeIconAndAmountList
                    (
                        UnitTypeName = "AA"
                        TextureToken = "UseInGame_Transport_flak"
                    )
                ),
                ([0, 6], UnitTypeIconAndAmountList
                    (
                        UnitTypeName = "AT"
                        TextureToken = "Texture_RTS_H_AT"
                    )
                ),
                ([0, 7], UnitTypeIconAndAmountList
                    (
                        UnitTypeName = "Artillery"
                        TextureToken = "UseInGame_Transport_howitzer"
                    )
                ),
                ([0, 8], UnitTypeIconAndAmountList
                    (
                        UnitTypeName = "Helicopter"
                        TextureToken = "UseInGame_Transport_Helo"
                    )
                ),
                ([1, 0], UnitTypeIconAndAmountList
                    (
                        UnitTypeName = "Air_AA"
                        TextureToken = "Texture_RTS_H_AA_air"
                    )
                ),
                ([1, 1], UnitTypeIconAndAmountList
                    (
                        UnitTypeName = "Air_SEAD"
                        TextureToken = "Texture_RTS_H_SEAD_air"
                    )
                ),
                ([1, 2], UnitTypeIconAndAmountList
                    (
                        UnitTypeName = "Air_AT"
                        TextureToken = "Texture_RTS_H_ATGM_air"
                    )
                ),
                ([1, 3], UnitTypeIconAndAmountList
                    (
                        UnitTypeName = "Air_Support"
                        TextureToken = "Texture_RTS_H_Support_air"
                    )
                ),
            ]
        )
    ]
)

//-------------------------------------------------------------------------------------

template DisplayAttackAndDefenseValueForPawn
[
    TextColor : string,
    ElementName : string,
    TextToken : string,
    TextToken_title : string,
    HintExtendedToken : string = '',
    HintBodyToken : string = '',
    HintTitleToken : string = '',
] is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        AlignementToFather = [0.0, 0.5]
        AlignementToAnchor = [0.0, 0.5]
    )

    Axis = ~/ListAxis/Vertical
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    FirstMargin = TRTTILength( Magnifiable = 2.0 )
    InterItemMargin = TRTTILength( Magnifiable = 2.0 )
    LastMargin = TRTTILength( Magnifiable = 2.0 )

    HasBackground = true
    BackgroundBlockColorToken = 'SM_RifleGreen'

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [85.0, 0.0]
                    AlignementToAnchor = [0.5, 0.0]
                    AlignementToFather = [0.5, 0.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                )

                TextStyle = "Default"

                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/FitToContent

                TypefaceToken = "Eurostyle"
                BigLineAction = ~/BigLineAction/MultiLine
                TextToken = <TextToken_title>
                TextDico = ~/LocalisationConstantes/dico_interface_ingame
                TextColor = 'SM_paleSilver'
                TextSize = "10"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = <ElementName>

                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToAnchor = [0.5, 0.0]
                    AlignementToFather = [0.5, 0.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                )

                TextStyle = "Default"

                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/FitToContent

                TypefaceToken = "Eurostyle_Medium"
                BigLineAction = ~/BigLineAction/MultiLine
                TextToken = <TextToken>
                TextDico = ~/LocalisationConstantes/dico_interface_ingame
                TextColor = <TextColor>
                TextSize = "16"
            )
        ),
    ]

    // Hint
    ForegroundComponents =
    [
        BUCKSpecificStrategicHintableArea
        (
            HintTitleToken = <HintTitleToken>
            HintBodyToken = <HintBodyToken>
            HintExtendedToken = <HintExtendedToken>
            DicoToken = ~/LocalisationConstantes/dico_interface_ingame
        )
    ]
)

//-------------------------------------------------------------------------------------

template UISpecificSelectionPanelPawnViewDescriptorTemplate
[
    MainComponentContainerUniqueName : string = "",
    SmartGroupsDisplayerAlignment : float2 = [0.0, 1.0],
    ContainsCubeAction : bool,
    HasSpotlight : bool,
    HQBackgroundBlockColorToken : string = "",

    EmptyBattlegroupDicoToken = "",
    EmptyBattlegroupNameToken = "",
    EmptyBattlegroupTextureName = "",
]
is TUISpecificSelectionPanelPawnViewDescriptor
(
    MainComponentContainerUniqueName = <MainComponentContainerUniqueName>
    MainComponentDescriptor = BUCKSpecificSelectionPanelPawnMainComponent
    (
        ContainsCubeAction = <ContainsCubeAction>
        HasSpotlight = <HasSpotlight>
    )

    SmartGroupsDisplayerDescriptor = UISpecificStrategicSmartGroupsDisplayerViewDescriptor
    (
        Alignment = <SmartGroupsDisplayerAlignment>
    )

    HQBackgroundBlockColorToken = <HQBackgroundBlockColorToken>

    EmptyBattlegroupDicoToken = <EmptyBattlegroupDicoToken>
    EmptyBattlegroupNameToken = <EmptyBattlegroupNameToken>
    EmptyBattlegroupTextureName = <EmptyBattlegroupTextureName>

    // à garder d'équerre avec StrategicBattleRole.ndf
    // la position dans le tableau correspond au role défini dans StrategicBattleRole.ndf (PanelListeGroupCombatRack pour les fighter donc en position 0 par exemple)
    PawnRoles =
    [
        "sm_comb", // Fighter
        "sm_auxb", // AuxiliarySupport
        "sm_airs", // AirSupport
        "sm_arts",  // GroundSupport
    ]
)

WaitUntilNextTurnButton is BUCKButtonDescriptor
(
    ElementName = "WaitUntilNextTurnButton"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [38.0, 32.0]
        AlignementToFather = [0.5, 0.5]
        AlignementToAnchor = [0.5, 0.5]
    )
    IsTogglable = true

    HasBorder = true
    BorderLineColorToken = 'ButtonHUD'
    BorderThicknessToken = '1'
    BordersToDraw = ~/TBorderSide/Default

    HasBackground = true
    BackgroundBlockColorToken = 'SM_Noir'

    Components =
    [
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [28.0, 28.0]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            TextureToken = "wait_until_next_turn"
            TextureColorToken = 'SM_paleSilver'
        ),

        BUCKSpecificStrategicHintableArea
        (
            HintTitleToken = "HNT_WAITUT"
            HintBodyToken = "HNT_WAITUB"
            HintExtendedToken = "HNT_WAITUE"
            DicoToken = ~/LocalisationConstantes/dico_interface_ingame
        )
    ]
    Mapping = $/KeyboardOption/Mapping_WaitUntilNextTurn
)

UIWaitUntilNextTurnViewDescriptor is TUISpecificWaitUntilNextTurnViewDescriptor
(
    MainComponentContainerUniqueName = "WaitUntilNextTurnContainer"
    MainComponentDescriptor = ~/WaitUntilNextTurnButton
)
